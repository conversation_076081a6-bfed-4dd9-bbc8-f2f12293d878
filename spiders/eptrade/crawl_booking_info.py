"""
亿通海运订舱平台：https://www.eptrade.cn/epb/login/scno_direct_bk.html
"""
import json
import os.path
import time
from queue import Queue
from threading import Thread

from tenacity import retry, stop_after_attempt, wait_fixed

from server import app
from spiders.eptrade import BaseEptradeSpider
from spiders.eptrade.config import CONTACTS, UPLOAD_EPTRADE_API
from utils import send_request, send_message_to_developer
from utils.util_operate_mysql import MysqlSpiderUtil
from utils.util_retry import return_after_retry
from utils.util_rocketmq import ConnRocket
from conf import HGJ_API_STATE_URL, CANCEL_API, MQ_UPDATE_BILL_NO_TAG, MQ_PRODUCER_ID, MQ_TOPIC, \
    SPIDER_MYSQL_CONFIG

CONSUMER_THEAD_NUM = 2
# 记录日志
logger = app.logger
file_name = os.path.basename(__file__)


class GetDataSpider(BaseEptradeSpider):
    def __init__(self):
        super().__init__()
        self.search_url = "https://www.eptrade.cn/epb/cdus.html"
        self.send_code = "339071940a"
        self.headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Accept-Language": "zh-CN,zh;q=0.9",
            "Connection": "keep-alive",
            "Sec-Fetch-Site": "same-origin",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\""
        }
        self.queue = Queue()
        self.mysql_spider = MysqlSpiderUtil(**SPIDER_MYSQL_CONFIG)

    def main(self):
        """主函数"""
        logger.info("亿通数据采集开始！")
        try:
            producer = Thread(target=self.get_data)
            producer.start()
            consumer_list = [Thread(target=self.handle_task) for _ in range(CONSUMER_THEAD_NUM)]
            for consumer in consumer_list:
                consumer.start()
        except Exception as e:
            logger.error(e, exc_info=True)
            message = f"{file_name}-亿通数据采集异常，请检查日志！"
            send_message_to_developer(message, CONTACTS)
        logger.info("亿通数据采集结束！")

    def get_data(self):
        """通过海管家订舱API获取相关数据"""
        page_num, page_size = 1, 20
        while True:
            data = {
                "businessPortCodes": ["SHANGHAI"],
                "carrierCodes": ["ONE"],
                "bookingStatuses": ["SUBMITTED_FOR_HGJ_REVIEW", "SENT_TO_SHIPPING_COMPANY_FOR_RELEASE",
                                    "SHIPPING_COMPANY_RELEASE"],
                "supplementStatuses": ["NO_SUBMITTED", "REJECTED", "SENT_TO_SHIPPING_COMPANY"]
            }
            response = self.upload_result(HGJ_API_STATE_URL, data)
            if not response:
                message = f"{file_name}-通过海管家订舱API获取相关数据失败!!!"
                send_message_to_developer(message, CONTACTS)
                logger.warning(f"通过海管家订舱API获取相关数据失败!!!")
            else:
                response_json = response.json()
                data = response_json["data"]
                for one in data["records"]:
                    self.queue.put(one)
                    logger.info(f"放入队列订舱单号信息: {one}")

                # 请求下一页
                if page_num * page_size < response_json["data"]["total"]:
                    page_num += 1
                else:
                    break
        for i in range(CONSUMER_THEAD_NUM):
            self.queue.put(None)

    def handle_task(self):
        """处理队列任务"""
        while True:
            task = self.queue.get()
            if task is None and self.queue.qsize() >= CONSUMER_THEAD_NUM:
                self.queue.put(task)
                continue
            if task is None and self.queue.qsize() < CONSUMER_THEAD_NUM:
                break
            logger.info(f"当前待查询委托编号: {task['delegationNo']}")
            self.search_booking(task)
            logger.info(f"订舱查询信息完成: {task['delegationNo']}")

    @retry(stop=stop_after_attempt(2), wait=wait_fixed(5), retry_error_callback=return_after_retry)
    def search_booking(self, task):
        """查询订舱信息"""
        search_headers = self.headers.update({
            "Content-Type": "application/x-www-form-urlencoded",
            "Origin": "https://www.eptrade.cn",
            "Referer": f"https://www.eptrade.cn/epb/cdBookList.html?ticket={self.cookies['ticket']}",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "X-Requested-With": "XMLHttpRequest",
        })
        search_params = {
            "method": "search1"
        }
        search_data = {
            "param.like.ebw2Booking.bookingNo": task["delegationNo"],
            "param.ebw2Booking.recvCode": "ONEY",
            "className": "com.easipass.ebw2.dao.model.Ebw2Booking ebw2Booking",
            "forward": "booking/common/cd_book_list",
            "param.ebw2Booking.sendCode": self.send_code,
            "param.ebw2Booking.cdbookStatus": "Y",
            "page": "1",
            "rows": "20",
            "sort": "updateTime",
            "order": "desc"
        }
        response = send_request(url=self.search_url, method="post", headers=search_headers,
                                cookies=self.cookies["cookies"], params=search_params, data=search_data)
        if response.status_code != 200:
            logger.warning(f"查询失败,返回状态码异常,状态码：{response.status_code},返回内容：{response.content}")
            send_message_to_developer(f"{file_name}-查询失败,返回状态码异常,请检查日志", CONTACTS)
        elif response.json()["total"] == 0:
            logger.info(f"未查询到该订舱信息,委托编号：{task['delegationNo']}")
            send_message_to_developer(f"{file_name}-未查询到该订舱信息,委托编号：{task['delegationNo']}", CONTACTS)
        elif response.json()["total"] > 1:
            logger.warning(f"查询到多个订舱信息,请检查数据:{response.json()}")
            send_message_to_developer(f"{file_name}-查询到多个订舱信息,请检查数据", CONTACTS)
        else:
            data = response.json()["rows"][0]
            self.handle_data(task, data)

    def handle_data(self, org_data, data):
        """处理返回数据"""
        bill_no = f"ONEY{data['loadingbillNo']}" if data.get("loadingbillNo") else ""
        reject_reason = data.get("eirResData")

        if reject_reason and bill_no:
            # 如果箱管回复为reject，则通知给订舱，不重复通知
            is_had = self.search_data(bill_no=bill_no)
            if is_had:
                logger.info(f"该提单号已存在，不再通知：{bill_no}")
            else:
                form_data = {
                    "pythonOperateType": "CONTAINER_MANAGEMENT_NOTIFY",
                    "carrierCode": org_data["carrierCode"],
                    "billNo": bill_no,
                    "refuseReason": reject_reason,
                }
                self.upload_result(url=UPLOAD_EPTRADE_API, data=form_data)
                self.insert_data(bill_no=bill_no, reject_reason=reject_reason, message=org_data)

        if data.get("status") == "c" and not reject_reason:  # 如果订舱状态为已取消且箱管回复为空
            # 执行拒绝订舱流程
            form_data = {
                "carrier": org_data["carrierCode"],
                "billNo": bill_no,
                "vessel": org_data["vessel"],
                "voyageNo": org_data["voyageNo"],
                "rejectedReason": data.get("carrierCancelReason"),
                "executionTime": time.strftime("%Y-%m-%d %H:%M:%S"),
                "source": "WEB"
            }
            self.upload_result(url=CANCEL_API, data=form_data)
        else:
            # 执行更新提单号流程
            if not bill_no:
                logger.warning(f"提单号为空，不执行补录")
                return
            else:
                booking_no = data.get("requestNo")  # 将唯一凭证号作为订舱号
                message = {"delegationNo": org_data["delegationNo"], "bookingId": booking_no, "billNo": bill_no,
                           "errorInfo": "", "flag": True}
                conn = ConnRocket()
                conn.produce_message(MQ_PRODUCER_ID, MQ_TOPIC, json.dumps(message), tags=MQ_UPDATE_BILL_NO_TAG,
                                     arg=int(time.time()))

    def search_data(self, bill_no):
        """判断是否已存在"""
        search_sql = "SELECT billNo FROM eptrade_reject_info WHERE billNo=%s"
        search_result = self.mysql_spider.get_one(search_sql, bill_no)
        return search_result

    def insert_data(self, bill_no, reject_reason, message):
        """插入数据"""
        insert_sql = "INSERT INTO eptrade_reject_info (bill_no, reject_reason, message) VALUES (%s, %s, %s)"
        self.mysql_spider.update(insert_sql, params=(bill_no, reject_reason, json.dumps(message, ensure_ascii=False)))


if __name__ == '__main__':
    spider = GetDataSpider()
    spider.main()
